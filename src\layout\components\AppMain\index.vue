<template>
  <keep-alive :include="cachedViews" :max="10">
    <router-view :key="key" style="height: calc(100% - 45px); width: 100%" />
  </keep-alive>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    // 需要缓存的页面
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      // 对于病人详情页面，使用病人ID作为key，确保不同病人有独立的实例
      if (this.$route.path.startsWith('/patient-detail/')) {
        return `PatientDetail-${this.$route.params.id}`
      }
      return this.$route.fullPath
    }
  }
}
</script>

<style lang="scss" scoped></style>
