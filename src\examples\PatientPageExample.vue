<!--
  病人页面使用新数据管理系统的示例
  展示如何在病人内页组件中使用新的数据隔离和事件系统
-->
<template>
  <div class="patient-page-example">
    <el-card>
      <div slot="header">
        <span>病人信息示例</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新数据
        </el-button>
      </div>
      
      <!-- 病人基本信息 -->
      <div v-if="patientInit.bingRenXM">
        <p><strong>病人姓名：</strong>{{ patientInit.bingRenXM }}</p>
        <p><strong>病人编号：</strong>{{ patientInit.bingRenBH }}</p>
        <p><strong>床位号：</strong>{{ patientInit.chuangWeiHao }}</p>
        <p><strong>专科：</strong>{{ patientInit.zhuanKeMC }}</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="isLoading">
        <el-loading-text>正在加载病人数据...</el-loading-text>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else>
        <el-alert title="暂无病人数据" type="info" show-icon />
      </div>
      
      <!-- 业务数据示例 -->
      <el-divider content-position="left">业务数据缓存示例</el-divider>
      <div>
        <el-input
          v-model="businessDataInput"
          placeholder="输入一些业务数据"
          style="width: 200px; margin-right: 10px;"
        />
        <el-button @click="saveBusinessData">保存到缓存</el-button>
        <el-button @click="loadBusinessData">从缓存加载</el-button>
        <p v-if="cachedBusinessData">
          <strong>缓存的数据：</strong>{{ cachedBusinessData }}
        </p>
      </div>
      
      <!-- 事件通信示例 -->
      <el-divider content-position="left">事件通信示例</el-divider>
      <div>
        <el-button @click="sendTestEvent">发送测试事件</el-button>
        <el-button @click="openOtherTab">打开其他标签页</el-button>
        <p v-if="receivedEvents.length > 0">
          <strong>接收到的事件：</strong>
          <ul>
            <li v-for="(event, index) in receivedEvents" :key="index">
              {{ event.name }}: {{ event.data }}
            </li>
          </ul>
        </p>
      </div>
    </el-card>
  </div>
</template>

<script>
import patientPageMixin from '@/mixins/patient-page-mixin'

export default {
  name: 'PatientPageExample',
  mixins: [patientPageMixin],
  
  data() {
    return {
      businessDataInput: '',
      cachedBusinessData: '',
      receivedEvents: []
    }
  },
  
  mounted() {
    // 监听测试事件
    this.onPatientEvent('testEvent', this.handleTestEvent)
    
    // 加载缓存的业务数据
    this.loadBusinessData()
  },
  
  methods: {
    // 刷新病人数据
    async refreshData() {
      await this.$store.dispatch('patientDetail/getPatientInit', this.bingLiID)
    },
    
    // 保存业务数据到缓存
    saveBusinessData() {
      if (this.businessDataInput.trim()) {
        this.setPatientBusinessData('exampleData', this.businessDataInput)
        this.$message.success('数据已保存到缓存')
        this.businessDataInput = ''
      }
    },
    
    // 从缓存加载业务数据
    loadBusinessData() {
      const data = this.getPatientBusinessData('exampleData')
      this.cachedBusinessData = data || ''
    },
    
    // 发送测试事件
    sendTestEvent() {
      const eventData = `测试数据 ${new Date().toLocaleTimeString()}`
      this.emitPatientEvent('testEvent', eventData)
    },
    
    // 处理测试事件
    handleTestEvent(data) {
      this.receivedEvents.unshift({
        name: 'testEvent',
        data: data,
        time: new Date().toLocaleTimeString()
      })
      
      // 只保留最近5个事件
      if (this.receivedEvents.length > 5) {
        this.receivedEvents = this.receivedEvents.slice(0, 5)
      }
    },
    
    // 打开其他标签页
    openOtherTab() {
      this.openPatientTab({
        name: 'PatientBasicInfo',
        component: () => import('@/views/patient-detail/PatientBasicInfo'),
        meta: {
          title: '病人详情'
        }
      })
    }
  }
}
</script>

<style scoped>
.patient-page-example {
  padding: 20px;
}

.el-loading-text {
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
