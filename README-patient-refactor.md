# 病人页面数据管理架构重构

## 🎯 重构目标

解决当前病人页面数据管理中的核心问题：
- ✅ 多个病人页面间数据污染
- ✅ EventBus事件冲突
- ✅ 重复请求接口影响性能
- ✅ keep-alive缓存机制不完善

## 🏗️ 新架构设计

### 核心组件

1. **PatientDataManager** (`src/utils/patient-data-manager.js`)
   - 为每个病人创建独立的数据存储空间
   - 管理数据生命周期和缓存策略
   - 提供数据隔离和状态管理

2. **PatientEventBus** (`src/utils/patient-event-bus.js`)
   - 为每个病人提供独立的事件系统
   - 避免多实例间的事件冲突
   - 支持全局事件和病人专用事件

3. **PatientDetail Store** (`src/store/modules/patientDetail.js`)
   - 增强的Vuex模块，支持多病人状态管理
   - 智能缓存和数据同步
   - 标签页状态管理

4. **PatientPageMixin** (`src/mixins/patient-page-mixin.js`)
   - 简化病人页面组件开发
   - 提供统一的数据访问接口
   - 自动处理生命周期管理

## 📁 文件结构

```
src/
├── utils/
│   ├── patient-data-manager.js      # 病人数据管理器
│   ├── patient-event-bus.js         # 病人事件总线
│   └── __tests__/
│       └── patient-data-manager.test.js
├── store/modules/
│   └── patientDetail.js             # 增强的病人详情store
├── mixins/
│   └── patient-page-mixin.js        # 病人页面混入
├── examples/
│   └── PatientPageExample.vue       # 使用示例
└── docs/
    └── patient-data-migration-guide.md
```

## 🚀 主要特性

### 1. 数据隔离
```javascript
// 每个病人拥有独立的数据空间
const patient1Data = patientDataManager.getPatientData('patient-001')
const patient2Data = patientDataManager.getPatientData('patient-002')
// patient1Data 和 patient2Data 完全独立
```

### 2. 智能缓存
```javascript
// 自动缓存，避免重复请求
await patientDataManager.initPatientData('patient-001') // 首次请求API
await patientDataManager.initPatientData('patient-001') // 使用缓存
```

### 3. 事件隔离
```javascript
// 病人专用事件，不会影响其他病人
patientEventBus.emit('sidebarClick', menuItem)
```

### 4. 简化开发
```javascript
// 使用混入简化组件开发
export default {
  mixins: [patientPageMixin],
  // 自动获得 patientInit, bingLiID 等计算属性
  // 自动处理数据初始化和事件清理
}
```

## 🔄 迁移指南

### 快速迁移步骤

1. **更新导入**
   ```javascript
   // 旧
   import { EventBus } from '@/utils/event-bus'
   
   // 新
   import patientEventBus from '@/utils/patient-event-bus'
   import patientPageMixin from '@/mixins/patient-page-mixin'
   ```

2. **使用混入**
   ```javascript
   export default {
     mixins: [patientPageMixin],
     // 自动获得病人相关的计算属性和方法
   }
   ```

3. **更新事件处理**
   ```javascript
   // 旧
   EventBus.$emit('sidebarClick', item)
   
   // 新
   this.emitPatientEvent('sidebarClick', item)
   ```

### 详细迁移指南
参考：`docs/patient-data-migration-guide.md`

## 🧪 测试

运行测试：
```bash
npm test src/utils/__tests__/patient-data-manager.test.js
```

## 📖 使用示例

查看完整示例：`src/examples/PatientPageExample.vue`

```javascript
// 基本使用
export default {
  mixins: [patientPageMixin],
  
  methods: {
    // 保存业务数据
    saveData() {
      this.setPatientBusinessData('myKey', this.formData)
    },
    
    // 发送事件
    openTab() {
      this.openPatientTab({
        name: 'SomeComponent',
        component: () => import('./SomeComponent'),
        meta: { title: '某个功能' }
      })
    }
  }
}
```

## 🔧 API参考

### PatientDataManager
- `getPatientData(patientId)` - 获取病人数据
- `initPatientData(patientId)` - 初始化病人数据
- `updatePatientData(patientId, key, value)` - 更新数据
- `clearPatientData(patientId)` - 清理数据

### PatientEventBus
- `emit(eventName, data)` - 发送事件
- `on(eventName, callback)` - 监听事件
- `off(eventName, callback)` - 移除监听
- `emitGlobal(eventName, data)` - 发送全局事件

### PatientPageMixin
- `patientInit` - 病人初始化数据
- `bingLiID` - 病人ID
- `emitPatientEvent()` - 发送病人事件
- `setPatientBusinessData()` - 设置业务数据
- `getPatientBusinessData()` - 获取业务数据

## 🎉 重构效果

### 解决的问题
- ✅ **数据污染**：每个病人拥有独立数据空间
- ✅ **事件冲突**：独立的EventBus实例
- ✅ **重复请求**：智能缓存机制
- ✅ **状态管理**：增强的Vuex store

### 性能提升
- 🚀 减少API请求次数
- 🚀 更快的页面切换速度
- 🚀 更好的内存管理
- 🚀 更稳定的用户体验

### 开发体验
- 🛠️ 简化的组件开发
- 🛠️ 统一的数据访问接口
- 🛠️ 自动的生命周期管理
- 🛠️ 完善的类型提示

## 🔮 后续计划

1. **渐进式迁移**：逐步将现有组件迁移到新系统
2. **性能监控**：添加性能监控和分析
3. **功能扩展**：根据使用反馈继续优化
4. **文档完善**：补充更多使用示例和最佳实践

## 📞 支持

如有问题或建议，请联系开发团队或查看相关文档。
