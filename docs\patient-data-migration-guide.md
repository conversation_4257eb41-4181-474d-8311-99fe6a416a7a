# 病人页面数据管理重构迁移指南

## 概述

本次重构解决了病人页面数据管理中的以下问题：
1. 多个病人页面间数据污染
2. EventBus事件冲突
3. 重复请求接口
4. 缓存机制不完善

## 新架构特性

### 1. 数据隔离
- 每个病人拥有独立的数据存储空间
- 切换病人时自动恢复对应的数据状态
- 避免不同病人间的数据互相污染

### 2. 智能缓存
- 病人数据按需加载，避免重复请求
- 支持数据预加载和懒加载
- 自动管理缓存生命周期

### 3. 事件隔离
- 每个病人拥有独立的EventBus实例
- 避免多实例间的事件串扰
- 支持全局事件和病人专用事件

## 迁移步骤

### 1. 更新组件导入

**旧代码：**
```javascript
import { EventBus } from '@/utils/event-bus'
import { mapState } from 'vuex'
```

**新代码：**
```javascript
import patientEventBus from '@/utils/patient-event-bus'
import patientPageMixin from '@/mixins/patient-page-mixin'
```

### 2. 使用混入简化开发

**旧代码：**
```javascript
export default {
  computed: {
    ...mapState({
      patientInit: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.$store.dispatch('patient/getPatientInit', this.bingLiID)
  }
}
```

**新代码：**
```javascript
export default {
  mixins: [patientPageMixin],
  // patientInit 和 bingLiID 已通过混入提供
  // 数据初始化已自动处理
}
```

### 3. 更新事件处理

**旧代码：**
```javascript
// 发送事件
EventBus.$emit('sidebarClick', menuItem)

// 监听事件
EventBus.$on('someEvent', this.handleEvent)

// 清理事件
EventBus.$off('someEvent')
```

**新代码：**
```javascript
// 发送事件
this.emitPatientEvent('sidebarClick', menuItem)
// 或直接使用
patientEventBus.emit('sidebarClick', menuItem)

// 监听事件
this.onPatientEvent('someEvent', this.handleEvent)

// 清理事件（混入会自动处理）
this.offPatientEvent('someEvent')
```

### 4. 更新数据获取方式

**旧代码：**
```javascript
// 获取病人数据
await this.$store.dispatch('patient/getPatientInit', bingLiID)
const patientData = this.$store.state.patient.patientInit
```

**新代码：**
```javascript
// 获取病人数据（自动缓存）
await this.$store.dispatch('patientDetail/getPatientInit', bingLiID)
const patientData = this.patientInit // 通过混入提供
```

### 5. 业务数据缓存

**新功能：**
```javascript
// 保存业务数据到病人缓存
this.setPatientBusinessData('myData', someValue)

// 从病人缓存获取业务数据
const data = this.getPatientBusinessData('myData')
```

## 核心API参考

### PatientDataManager

```javascript
import patientDataManager from '@/utils/patient-data-manager'

// 获取病人数据
const patientData = patientDataManager.getPatientData(patientId)

// 初始化病人数据
await patientDataManager.initPatientData(patientId)

// 检查数据状态
const isLoaded = patientDataManager.isPatientDataLoaded(patientId)
const isLoading = patientDataManager.isPatientDataLoading(patientId)
```

### PatientEventBus

```javascript
import patientEventBus from '@/utils/patient-event-bus'

// 发送事件给当前病人
patientEventBus.emit('eventName', data)

// 发送事件给指定病人
patientEventBus.emitToPatient(patientId, 'eventName', data)

// 监听当前病人事件
patientEventBus.on('eventName', callback)

// 发送全局事件
patientEventBus.emitGlobal('eventName', data)
```

### Store Actions

```javascript
// 切换到指定病人
await this.$store.dispatch('patientDetail/switchToPatient', patientId)

// 获取病人初始化数据
await this.$store.dispatch('patientDetail/getPatientInit', patientId)

// 管理标签页
this.$store.dispatch('patientDetail/addVisitedView', view)
this.$store.dispatch('patientDetail/setActiveRoute', routeName)
```

## 注意事项

1. **向后兼容性**：旧的EventBus仍然可用，但建议逐步迁移
2. **性能优化**：新系统会自动缓存数据，避免重复请求
3. **内存管理**：系统会自动清理不再使用的病人数据
4. **调试支持**：可通过`patientEventBus.getDebugInfo()`获取调试信息

## 常见问题

### Q: 如何处理现有的EventBus监听器？
A: 可以逐步迁移，新旧系统可以并存。建议优先迁移新开发的组件。

### Q: 数据缓存会占用太多内存吗？
A: 系统会自动管理缓存，当病人页面关闭时会清理对应数据。

### Q: 如何在多个病人间共享数据？
A: 使用`patientEventBus.emitGlobal()`发送全局事件，或使用原有的Vuex store。

## 示例代码

参考 `src/examples/PatientPageExample.vue` 查看完整的使用示例。
