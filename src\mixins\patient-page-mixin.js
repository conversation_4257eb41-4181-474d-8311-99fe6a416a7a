/**
 * 病人页面混入
 * 为病人内页组件提供统一的数据管理和事件处理
 */

import { mapState, mapGetters } from 'vuex'
import patientEventBus from '@/utils/patient-event-bus'
import patientDataManager from '@/utils/patient-data-manager'

export default {
  computed: {
    ...mapState('patientDetail', ['currentPatientId', 'isLoading', 'isLoaded']),
    ...mapGetters('patientDetail', ['currentPatientInit', 'isCurrentPatientLoaded']),

    // 获取病人ID
    bingLiID() {
      return this.$route.params.id || this.currentPatientId
    },

    // 获取病人初始化数据
    patientInit() {
      return this.currentPatientInit
    }
  },

  methods: {
    /**
     * 发送病人专用事件
     * @param {string} eventName 事件名称
     * @param {any} data 事件数据
     */
    emitPatientEvent(eventName, data) {
      patientEventBus.emit(eventName, data)
    },

    /**
     * 监听病人专用事件
     * @param {string} eventName 事件名称
     * @param {Function} callback 回调函数
     */
    onPatientEvent(eventName, callback) {
      patientEventBus.on(eventName, callback)
    },

    /**
     * 移除病人专用事件监听
     * @param {string} eventName 事件名称
     * @param {Function} callback 回调函数（可选）
     */
    offPatientEvent(eventName, callback) {
      patientEventBus.off(eventName, callback)
    },

    /**
     * 获取病人的业务数据
     * @param {string} key 数据键
     * @returns {any} 数据值
     */
    getPatientBusinessData(key) {
      if (!this.bingLiID) return null
      const patientData = patientDataManager.getPatientData(this.bingLiID)
      return patientData.businessData[key]
    },

    /**
     * 设置病人的业务数据
     * @param {string} key 数据键
     * @param {any} value 数据值
     */
    setPatientBusinessData(key, value) {
      if (!this.bingLiID) return
      const patientData = patientDataManager.getPatientData(this.bingLiID)
      patientData.businessData[key] = value
    },

    /**
     * 初始化病人数据（如果需要）
     * @returns {Promise} 初始化Promise
     */
    async initPatientDataIfNeeded() {
      if (!this.bingLiID) return

      if (!this.isCurrentPatientLoaded) {
        await this.$store.dispatch('patientDetail/getPatientInit', this.bingLiID)
      }
    },

    /**
     * 打开新的病人内页标签
     * @param {Object} menuItem 菜单项配置
     */
    openPatientTab(menuItem) {
      this.emitPatientEvent('sidebarClick', menuItem)
    }
  },

  data() {
    return {
      _patientMixinInitialized: false
    }
  },

  async created() {
    // 防止重复初始化
    if (this._patientMixinInitialized) {
      return
    }
    this._patientMixinInitialized = true

    console.log(
      `PatientPageMixin: 初始化组件 ${this.$options.name || 'Unknown'} for 病人 ${this.bingLiID}`
    )

    // 确保病人数据已初始化
    if (this.bingLiID) {
      await this.initPatientDataIfNeeded()
    }
  },

  beforeDestroy() {
    console.log(
      `PatientPageMixin: 销毁组件 ${this.$options.name || 'Unknown'} for 病人 ${this.bingLiID}`
    )
    // 注意：不要清理所有事件监听器，因为其他组件可能还在使用
    // 只清理当前组件相关的监听器
  }
}
