# 病人数据隔离问题修复报告

## 🐛 问题分析

### 根本原因
之前的简化方案虽然创建了`SimplePatientManager`来管理标签页状态，但仍然使用全局的`EventBus`，导致以下问题：

1. **EventBus全局污染**：所有病人页面实例都监听同一个`sidebarClick`事件
2. **事件广播问题**：病人A点击侧边栏时，所有病人页面的TagsView组件都会收到事件
3. **状态交叉保存**：每个TagsView实例都会执行`addTags`并保存状态，导致数据污染

### 问题表现
- 病人A点击"病程记录" → 病人B的标签页中也出现"病程记录"
- 不同病人的二级标签页状态互相影响
- 数据隔离机制失效

## 🛠️ 修复方案

### 1. 为每个病人创建独立的EventBus实例

**修改文件：** `src/utils/simple-patient-manager.js`

```javascript
class SimplePatientManager {
  constructor() {
    this.patientTabsState = new Map()
    this.patientEventBusMap = new Map()  // 新增：存储每个病人的EventBus
    this.currentPatientId = null         // 新增：当前活跃病人ID
  }

  getPatientEventBus(patientId) {
    if (!this.patientEventBusMap.has(patientId)) {
      this.patientEventBusMap.set(patientId, new Vue())
    }
    return this.patientEventBusMap.get(patientId)
  }
}
```

### 2. 修改TagsView组件使用病人专用EventBus

**修改文件：** `src/views/patient-detail/components/TagsView/index.vue`

```javascript
computed: {
  // 获取当前病人的EventBus实例
  patientEventBus() {
    return this.bingLiID ? simplePatientManager.getPatientEventBus(this.bingLiID) : null
  }
},

mounted() {
  // 使用病人专用的EventBus监听事件
  if (this.patientEventBus) {
    this.patientEventBus.$on('sidebarClick', (menu) => {
      console.log(`病人 ${this.bingLiID} 接收到sidebarClick事件:`, menu.name)
      this.addTags(menu)
    })
  }
}
```

### 3. 修改Sidebar组件使用病人专用EventBus

**修改文件：** `src/views/patient-detail/components/Sidebar/index.vue`

```javascript
computed: {
  // 获取当前病人的EventBus实例
  patientEventBus() {
    return this.bingLiID ? simplePatientManager.getPatientEventBus(this.bingLiID) : null
  }
},

methods: {
  async itemClickNative(item) {
    // 使用病人专用的EventBus发送事件
    if (this.patientEventBus) {
      console.log(`病人 ${this.bingLiID} 发送sidebarClick事件:`, item.name)
      this.patientEventBus.$emit('sidebarClick', item)
    }
  }
}
```

### 4. 添加病人切换监听

**修改文件：** `src/views/patient-detail/components/TagsView/index.vue`

```javascript
watch: {
  bingLiID: {
    immediate: true,
    handler(newId, oldId) {
      if (newId && newId !== oldId) {
        console.log(`TagsView: 病人ID从 ${oldId} 切换到 ${newId}`)
        // 设置当前活跃病人
        simplePatientManager.setCurrentPatient(newId)
        // 重新初始化标签页
        this.restoreOrInitTags()
      }
    }
  }
}
```

## 🧪 验证方法

### 1. 使用调试工具

```javascript
// 在浏览器控制台中运行
window.patientIsolationDebug.runFullIsolationTest('patient-001', 'patient-002')
```

### 2. 手动测试步骤

1. **打开两个不同的病人页面**
   - 病人A：`/patient-detail/patient-001`
   - 病人B：`/patient-detail/patient-002`

2. **在病人A中点击侧边栏菜单**
   - 点击"病程记录"
   - 观察病人A的标签页中是否出现"病程记录"

3. **切换到病人B**
   - 检查病人B的标签页中是否**没有**出现"病程记录"
   - 病人B应该只有默认的"住院医嘱"标签

4. **在病人B中点击不同的菜单**
   - 点击"医嘱管理"
   - 观察病人B的标签页中是否出现"医嘱管理"

5. **切换回病人A**
   - 检查病人A的标签页状态是否保持不变
   - 应该仍然有"住院医嘱"和"病程记录"两个标签
   - 不应该有"医嘱管理"标签

### 3. 控制台日志验证

修复后，在控制台中应该看到类似的日志：

```
病人 patient-001 发送sidebarClick事件: ProgressNote
病人 patient-001 接收到sidebarClick事件: ProgressNote
为病人 patient-001 创建独立的EventBus
设置当前活跃病人: patient-001
```

## 📊 修复效果

### 修复前
- ❌ 全局EventBus导致事件广播到所有病人实例
- ❌ 病人A的操作影响病人B的标签页
- ❌ 数据隔离机制失效

### 修复后
- ✅ 每个病人拥有独立的EventBus实例
- ✅ 事件只在对应病人的组件间传递
- ✅ 完全的数据隔离，互不影响

## 🔧 技术细节

### EventBus隔离机制
```javascript
// 病人A的EventBus
const eventBusA = simplePatientManager.getPatientEventBus('patient-001')

// 病人B的EventBus  
const eventBusB = simplePatientManager.getPatientEventBus('patient-002')

// eventBusA !== eventBusB (不同的Vue实例)
```

### 状态管理流程
1. 用户点击病人A的侧边栏菜单
2. 病人A的Sidebar组件使用`eventBusA`发送事件
3. 只有病人A的TagsView组件接收到事件
4. 病人A的TagsView组件更新标签页并保存状态
5. 病人B的组件完全不受影响

### 内存管理
- 当病人主标签页关闭时，自动清理对应的EventBus实例
- 避免内存泄漏和事件监听器累积

## 🎯 总结

这次修复彻底解决了病人间数据污染的问题：

1. **根本原因**：全局EventBus导致的事件广播问题
2. **解决方案**：为每个病人创建独立的EventBus实例
3. **验证方法**：提供了完整的调试工具和测试步骤
4. **修复效果**：实现了完全的病人数据隔离

现在每个病人的二级标签页状态完全独立，病人A的操作不会影响病人B，反之亦然。
