<template>
  <div class="inner-container">
    <el-container>
      <el-aside :class="{ 'el-aside__collapse': !opened }">
        <sidebar />
      </el-aside>
      <el-main>
        <patient-detail-view />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import PatientDetailView from '@/views/patient-detail/PatientDetailView.vue'
import Sidebar from '@/views/patient-detail/components/Sidebar/index.vue'
import store from '@/store'
export default {
  name: 'PatientDetail',
  components: {
    Sidebar,
    PatientDetailView
  },
  data() {
    return {}
  },
  computed: {
    sidebar() {
      return store.state.app.sidebar
    },
    opened: {
      get() {
        return this.sidebar.opened
      },
      set(val) {
        if (!val) this.toggleSideBar()
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.inner-container {
  .el-aside {
    flex-basis: 235px;
  }

  .el-main {
    display: flex;
    height: 100%;
    padding: 0px;

    .app-main {
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
}
</style>
