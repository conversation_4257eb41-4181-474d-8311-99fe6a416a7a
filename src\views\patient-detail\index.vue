<template>
  <div class="inner-container">
    <el-container>
      <el-aside :class="{ 'el-aside__collapse': !opened }">
        <sidebar />
      </el-aside>
      <el-main>
        <patient-detail-view />
      </el-main>
    </el-container>
  </div>
</template>

<script>
import PatientDetailView from '@/views/patient-detail/PatientDetailView.vue'
import Sidebar from '@/views/patient-detail/components/Sidebar/index.vue'
import store from '@/store'
export default {
  name: 'PatientDetail',
  components: {
    Sidebar,
    PatientDetailView
  },
  // 动态组件名称，确保不同病人有独立的缓存实例
  beforeCreate() {
    const patientId = this.$route.params.id
    if (patientId) {
      this.$options.name = `PatientDetail-${patientId}`
    }
  },
  data() {
    return {}
  },
  computed: {
    sidebar() {
      return store.state.app.sidebar
    },
    opened: {
      get() {
        return this.sidebar.opened
      },
      set(val) {
        if (!val) this.toggleSideBar()
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.inner-container {
  .el-aside {
    flex-basis: 235px;
  }

  .el-main {
    display: flex;
    height: 100%;
    padding: 0px;

    .app-main {
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
}
</style>
