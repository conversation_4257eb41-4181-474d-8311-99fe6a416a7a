/**
 * 病人数据管理调试工具
 * 用于调试和监控病人数据管理系统的状态
 */

import patientDataManager from './patient-data-manager'
import patientEventBus from './patient-event-bus'
import store from '@/store'

class PatientDebugHelper {
  constructor() {
    this.logs = []
    this.maxLogs = 100
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 记录调试日志
   * @param {string} type 日志类型
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  log(type, message, data = null) {
    if (!this.isEnabled) return

    const logEntry = {
      timestamp: new Date().toISOString(),
      type,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    }

    this.logs.unshift(logEntry)
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs)
    }

    console.log(`[PatientDebug:${type}] ${message}`, data || '')
  }

  /**
   * 获取当前系统状态
   * @returns {Object} 系统状态信息
   */
  getSystemState() {
    const currentPatientId = patientDataManager.getCurrentPatientId()
    const cachedPatientIds = patientDataManager.getCachedPatientIds()
    const storeState = store.state.patientDetail

    return {
      currentPatientId,
      cachedPatientIds,
      cachedPatientsCount: cachedPatientIds.length,
      storeState: {
        currentPatientId: storeState.currentPatientId,
        activeRoute: storeState.activeRoute,
        visitedViewsCount: storeState.visitedViews.length,
        isLoading: storeState.isLoading,
        isLoaded: storeState.isLoaded
      },
      eventBusInfo: patientEventBus.getDebugInfo()
    }
  }

  /**
   * 获取指定病人的详细信息
   * @param {string} patientId 病人ID
   * @returns {Object} 病人详细信息
   */
  getPatientDetails(patientId) {
    if (!patientId) {
      patientId = patientDataManager.getCurrentPatientId()
    }

    if (!patientId) {
      return { error: '没有指定病人ID且没有当前活跃病人' }
    }

    const patientData = patientDataManager.getPatientData(patientId)
    const isLoaded = patientDataManager.isPatientDataLoaded(patientId)
    const isLoading = patientDataManager.isPatientDataLoading(patientId)

    return {
      patientId,
      isLoaded,
      isLoading,
      patientData: {
        hasPatientInit: !!patientData.patientInit && Object.keys(patientData.patientInit).length > 0,
        patientInitKeys: patientData.patientInit ? Object.keys(patientData.patientInit) : [],
        activeRoute: patientData.activeRoute,
        visitedViewsCount: patientData.visitedViews ? patientData.visitedViews.length : 0,
        visitedViews: patientData.visitedViews ? patientData.visitedViews.map(v => ({
          name: v.name,
          title: v.title
        })) : [],
        businessDataKeys: Object.keys(patientData.businessData || {})
      }
    }
  }

  /**
   * 检查数据一致性
   * @returns {Object} 一致性检查结果
   */
  checkDataConsistency() {
    const issues = []
    const currentPatientId = patientDataManager.getCurrentPatientId()
    const storeState = store.state.patientDetail

    // 检查当前病人ID一致性
    if (currentPatientId !== storeState.currentPatientId) {
      issues.push({
        type: 'currentPatientId_mismatch',
        message: `PatientDataManager当前病人ID (${currentPatientId}) 与Store不一致 (${storeState.currentPatientId})`
      })
    }

    // 检查当前病人的数据一致性
    if (currentPatientId) {
      const patientData = patientDataManager.getPatientData(currentPatientId)
      
      // 检查visitedViews一致性
      if (patientData.visitedViews.length !== storeState.visitedViews.length) {
        issues.push({
          type: 'visitedViews_count_mismatch',
          message: `病人 ${currentPatientId} 的visitedViews数量不一致: Manager(${patientData.visitedViews.length}) vs Store(${storeState.visitedViews.length})`
        })
      }

      // 检查activeRoute一致性
      if (patientData.activeRoute !== storeState.activeRoute) {
        issues.push({
          type: 'activeRoute_mismatch',
          message: `病人 ${currentPatientId} 的activeRoute不一致: Manager(${patientData.activeRoute}) vs Store(${storeState.activeRoute})`
        })
      }
    }

    return {
      isConsistent: issues.length === 0,
      issues,
      checkedAt: new Date().toISOString()
    }
  }

  /**
   * 获取最近的调试日志
   * @param {number} count 日志数量
   * @returns {Array} 日志列表
   */
  getRecentLogs(count = 20) {
    return this.logs.slice(0, count)
  }

  /**
   * 清理调试日志
   */
  clearLogs() {
    this.logs = []
  }

  /**
   * 强制刷新指定病人的数据
   * @param {string} patientId 病人ID
   */
  async forceRefreshPatient(patientId) {
    if (!patientId) {
      patientId = patientDataManager.getCurrentPatientId()
    }

    if (!patientId) {
      this.log('error', '无法强制刷新：没有指定病人ID')
      return
    }

    this.log('info', `强制刷新病人 ${patientId} 的数据`)
    
    try {
      // 清除缓存状态
      const patientData = patientDataManager.getPatientData(patientId)
      patientData.isLoaded = false
      patientData.isLoading = false
      
      // 重新加载数据
      await store.dispatch('patientDetail/getPatientInit', patientId)
      
      this.log('success', `病人 ${patientId} 数据刷新成功`)
    } catch (error) {
      this.log('error', `病人 ${patientId} 数据刷新失败`, error)
    }
  }

  /**
   * 导出调试报告
   * @returns {Object} 调试报告
   */
  exportDebugReport() {
    return {
      timestamp: new Date().toISOString(),
      systemState: this.getSystemState(),
      consistencyCheck: this.checkDataConsistency(),
      recentLogs: this.getRecentLogs(50),
      patientDetails: patientDataManager.getCachedPatientIds().map(id => 
        this.getPatientDetails(id)
      )
    }
  }
}

// 创建单例实例
const patientDebugHelper = new PatientDebugHelper()

// 在开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.patientDebugHelper = patientDebugHelper
}

export default patientDebugHelper
