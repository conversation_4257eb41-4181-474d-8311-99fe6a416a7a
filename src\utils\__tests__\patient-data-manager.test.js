/**
 * 病人数据管理器测试
 */

import patientDataManager from '../patient-data-manager'

// Mock API
jest.mock('@/api/patient-info', () => ({
  PatientInit: jest.fn()
}))

describe('PatientDataManager', () => {
  beforeEach(() => {
    // 清理所有数据
    patientDataManager.patientDataMap.clear()
    patientDataManager.patientEventBusMap.clear()
    patientDataManager.currentPatientId = null
  })

  test('应该为新病人创建数据存储空间', () => {
    const patientId = 'patient-001'
    const patientData = patientDataManager.getPatientData(patientId)
    
    expect(patientData).toBeDefined()
    expect(patientData.patientInit).toEqual({})
    expect(patientData.visitedViews).toEqual([])
    expect(patientData.isLoaded).toBe(false)
  })

  test('应该为新病人创建独立的EventBus', () => {
    const patientId1 = 'patient-001'
    const patientId2 = 'patient-002'
    
    const eventBus1 = patientDataManager.getPatientEventBus(patientId1)
    const eventBus2 = patientDataManager.getPatientEventBus(patientId2)
    
    expect(eventBus1).toBeDefined()
    expect(eventBus2).toBeDefined()
    expect(eventBus1).not.toBe(eventBus2)
  })

  test('应该正确设置和获取当前病人ID', () => {
    const patientId = 'patient-001'
    
    patientDataManager.setCurrentPatient(patientId)
    expect(patientDataManager.getCurrentPatientId()).toBe(patientId)
  })

  test('应该正确更新病人数据', () => {
    const patientId = 'patient-001'
    const testData = { name: '测试病人' }
    
    patientDataManager.updatePatientData(patientId, 'patientInit', testData)
    
    const patientData = patientDataManager.getPatientData(patientId)
    expect(patientData.patientInit).toEqual(testData)
  })

  test('应该正确获取病人的特定数据', () => {
    const patientId = 'patient-001'
    const testRoute = 'TestRoute'
    
    patientDataManager.updatePatientData(patientId, 'activeRoute', testRoute)
    
    const activeRoute = patientDataManager.getPatientDataValue(patientId, 'activeRoute')
    expect(activeRoute).toBe(testRoute)
  })

  test('应该正确检查数据加载状态', () => {
    const patientId = 'patient-001'
    
    // 初始状态
    expect(patientDataManager.isPatientDataLoaded(patientId)).toBe(false)
    expect(patientDataManager.isPatientDataLoading(patientId)).toBe(false)
    
    // 设置加载状态
    patientDataManager.updatePatientData(patientId, 'isLoading', true)
    expect(patientDataManager.isPatientDataLoading(patientId)).toBe(true)
    
    // 设置已加载状态
    patientDataManager.updatePatientData(patientId, 'isLoaded', true)
    patientDataManager.updatePatientData(patientId, 'isLoading', false)
    expect(patientDataManager.isPatientDataLoaded(patientId)).toBe(true)
    expect(patientDataManager.isPatientDataLoading(patientId)).toBe(false)
  })

  test('应该正确清理病人数据', () => {
    const patientId = 'patient-001'
    
    // 创建数据
    patientDataManager.getPatientData(patientId)
    patientDataManager.getPatientEventBus(patientId)
    
    expect(patientDataManager.patientDataMap.has(patientId)).toBe(true)
    expect(patientDataManager.patientEventBusMap.has(patientId)).toBe(true)
    
    // 清理数据
    patientDataManager.clearPatientData(patientId)
    
    expect(patientDataManager.patientDataMap.has(patientId)).toBe(false)
    expect(patientDataManager.patientEventBusMap.has(patientId)).toBe(false)
  })

  test('应该返回正确的缓存病人ID列表', () => {
    const patientIds = ['patient-001', 'patient-002', 'patient-003']
    
    // 创建数据
    patientIds.forEach(id => {
      patientDataManager.getPatientData(id)
    })
    
    const cachedIds = patientDataManager.getCachedPatientIds()
    expect(cachedIds).toEqual(expect.arrayContaining(patientIds))
    expect(cachedIds.length).toBe(patientIds.length)
  })
})
