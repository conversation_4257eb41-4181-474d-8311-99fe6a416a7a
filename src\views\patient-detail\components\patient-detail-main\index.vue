<template>
  <transition mode="out-in" name="fade-transform">
    <keep-alive :include="cachedViews">
      <router-view :key="key" style="height: 100%; width: 100%" />
    </keep-alive>
  </transition>
</template>

<script>
export default {
  name: 'PatientDetailMain',
  computed: {
    // 需要缓存的页面 固钉
    cachedViews() {
      return this.$store.state.tagsView.patientCachedViews
    },
    key() {
      return this.$route.fullPath
    },
    title() {
      return this.$route.meta.title
    }
  }
}
</script>

<style lang="scss" scoped></style>
