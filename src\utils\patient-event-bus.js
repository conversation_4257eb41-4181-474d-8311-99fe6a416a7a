/**
 * 病人事件总线
 * 为每个病人提供独立的事件系统，避免多实例间的事件冲突
 */

import Vue from 'vue'
import patientDataManager from './patient-data-manager'

class PatientEventBus {
  constructor() {
    // 全局事件总线（用于跨病人的通信）
    this.globalEventBus = new Vue()
  }

  /**
   * 获取指定病人的EventBus实例
   * @param {string} patientId 病人ID
   * @returns {Vue} EventBus实例
   */
  getPatientEventBus(patientId) {
    return patientDataManager.getPatientEventBus(patientId)
  }

  /**
   * 获取当前活跃病人的EventBus实例
   * @returns {Vue} EventBus实例
   */
  getCurrentPatientEventBus() {
    const currentPatientId = patientDataManager.getCurrentPatientId()
    if (!currentPatientId) {
      console.warn('没有当前活跃的病人，使用全局EventBus')
      return this.globalEventBus
    }
    return this.getPatientEventBus(currentPatientId)
  }

  /**
   * 安全地为当前活跃病人发送事件（防止重复）
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   * @param {boolean} once 是否只发送一次
   */
  emitSafe(eventName, data, once = false) {
    const currentPatientId = patientDataManager.getCurrentPatientId()
    if (!currentPatientId) {
      console.warn('没有当前活跃的病人，跳过事件发送')
      return
    }

    if (once) {
      // 使用时间戳防止短时间内重复发送
      const key = `${currentPatientId}_${eventName}_${Date.now()}`
      if (this._recentEvents && this._recentEvents.has(key)) {
        return
      }

      if (!this._recentEvents) {
        this._recentEvents = new Set()
      }
      this._recentEvents.add(key)

      // 清理过期的事件记录
      setTimeout(() => {
        this._recentEvents.delete(key)
      }, 1000)
    }

    const eventBus = this.getPatientEventBus(currentPatientId)
    eventBus.$emit(eventName, data)
  }

  /**
   * 为指定病人发送事件
   * @param {string} patientId 病人ID
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emitToPatient(patientId, eventName, data) {
    const eventBus = this.getPatientEventBus(patientId)
    eventBus.$emit(eventName, data)
  }

  /**
   * 为当前活跃病人发送事件
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emit(eventName, data) {
    const eventBus = this.getCurrentPatientEventBus()
    eventBus.$emit(eventName, data)
  }

  /**
   * 为指定病人监听事件
   * @param {string} patientId 病人ID
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  onForPatient(patientId, eventName, callback) {
    const eventBus = this.getPatientEventBus(patientId)
    eventBus.$on(eventName, callback)
  }

  /**
   * 为当前活跃病人监听事件
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  on(eventName, callback) {
    const eventBus = this.getCurrentPatientEventBus()
    eventBus.$on(eventName, callback)
  }

  /**
   * 为指定病人移除事件监听
   * @param {string} patientId 病人ID
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数（可选）
   */
  offForPatient(patientId, eventName, callback) {
    const eventBus = this.getPatientEventBus(patientId)
    eventBus.$off(eventName, callback)
  }

  /**
   * 为当前活跃病人移除事件监听
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数（可选）
   */
  off(eventName, callback) {
    const eventBus = this.getCurrentPatientEventBus()
    eventBus.$off(eventName, callback)
  }

  /**
   * 发送全局事件（跨所有病人）
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emitGlobal(eventName, data) {
    this.globalEventBus.$emit(eventName, data)
  }

  /**
   * 监听全局事件
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  onGlobal(eventName, callback) {
    this.globalEventBus.$on(eventName, callback)
  }

  /**
   * 移除全局事件监听
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数（可选）
   */
  offGlobal(eventName, callback) {
    this.globalEventBus.$off(eventName, callback)
  }

  /**
   * 清理指定病人的所有事件监听器
   * @param {string} patientId 病人ID
   */
  clearPatientEvents(patientId) {
    const eventBus = patientDataManager.getPatientEventBus(patientId)
    if (eventBus) {
      eventBus.$off() // 移除所有监听器
    }
  }

  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  getDebugInfo() {
    return {
      currentPatientId: patientDataManager.getCurrentPatientId(),
      cachedPatientIds: patientDataManager.getCachedPatientIds(),
      globalEventBusListeners: this.globalEventBus._events || {}
    }
  }
}

// 创建单例实例
const patientEventBus = new PatientEventBus()

export default patientEventBus

// 为了兼容性，也导出一些常用方法
export const emit = (eventName, data) => patientEventBus.emit(eventName, data)
export const on = (eventName, callback) => patientEventBus.on(eventName, callback)
export const off = (eventName, callback) => patientEventBus.off(eventName, callback)
