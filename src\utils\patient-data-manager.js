/**
 * 病人数据管理器
 * 为每个病人创建独立的数据存储空间，避免数据污染
 */

import Vue from 'vue'
import { PatientInit } from '@/api/patient-info'

class PatientDataManager {
  constructor() {
    // 存储每个病人的数据
    this.patientDataMap = new Map()
    // 存储每个病人的EventBus实例
    this.patientEventBusMap = new Map()
    // 当前活跃的病人ID
    this.currentPatientId = null
  }

  /**
   * 获取病人的数据存储空间
   * @param {string} patientId 病人ID
   * @returns {Object} 病人数据对象
   */
  getPatientData(patientId) {
    if (!this.patientDataMap.has(patientId)) {
      // 为新病人创建数据存储空间
      this.patientDataMap.set(patientId, {
        // 病人基本信息
        patientInit: {},
        // 侧边栏路由状态
        sideBarRoute: '',
        // 标签页状态
        visitedViews: [],
        activeRoute: '',
        // 加载状态
        isLoading: false,
        isLoaded: false,
        // 其他业务数据
        businessData: {}
      })
    }
    return this.patientDataMap.get(patientId)
  }

  /**
   * 获取病人的EventBus实例
   * @param {string} patientId 病人ID
   * @returns {Vue} EventBus实例
   */
  getPatientEventBus(patientId) {
    if (!this.patientEventBusMap.has(patientId)) {
      // 为新病人创建独立的EventBus实例
      this.patientEventBusMap.set(patientId, new Vue())
    }
    return this.patientEventBusMap.get(patientId)
  }

  /**
   * 设置当前活跃的病人
   * @param {string} patientId 病人ID
   */
  setCurrentPatient(patientId) {
    this.currentPatientId = patientId
  }

  /**
   * 获取当前活跃的病人ID
   * @returns {string} 病人ID
   */
  getCurrentPatientId() {
    return this.currentPatientId
  }

  /**
   * 初始化病人数据
   * @param {string} patientId 病人ID
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise} 初始化Promise
   */
  async initPatientData(patientId, forceRefresh = false) {
    const patientData = this.getPatientData(patientId)

    // 如果正在加载中，等待加载完成
    if (patientData.isLoading) {
      console.log(`病人 ${patientId} 数据正在加载中，等待完成...`)
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!patientData.isLoading) {
            resolve(patientData.patientInit)
          } else {
            setTimeout(checkLoading, 100)
          }
        }
        checkLoading()
      })
    }

    // 如果已经加载过且不强制刷新，直接返回缓存数据
    if (
      patientData.isLoaded &&
      !forceRefresh &&
      patientData.patientInit &&
      Object.keys(patientData.patientInit).length > 0
    ) {
      console.log(`使用病人 ${patientId} 的缓存数据`)
      return patientData.patientInit
    }

    // 设置加载状态
    patientData.isLoading = true
    console.log(`开始加载病人 ${patientId} 的数据...`)

    try {
      // 调用初始化接口
      const res = await PatientInit({ bingLiID: patientId })
      if (res.hasError === 0 && res.data) {
        patientData.patientInit = res.data
        patientData.isLoaded = true
        console.log(`病人 ${patientId} 数据加载成功`)
      } else {
        console.warn(`病人 ${patientId} 数据加载失败:`, res.errorMsg || '未知错误')
        patientData.isLoaded = true // 标记为已尝试加载，避免重复请求
      }
    } catch (error) {
      console.error(`初始化病人 ${patientId} 数据失败:`, error)
      patientData.isLoaded = true // 标记为已尝试加载，避免重复请求
    } finally {
      patientData.isLoading = false
    }

    return patientData.patientInit
  }

  /**
   * 更新病人数据
   * @param {string} patientId 病人ID
   * @param {string} key 数据键
   * @param {any} value 数据值
   */
  updatePatientData(patientId, key, value) {
    const patientData = this.getPatientData(patientId)
    patientData[key] = value
  }

  /**
   * 获取病人的特定数据
   * @param {string} patientId 病人ID
   * @param {string} key 数据键
   * @returns {any} 数据值
   */
  getPatientDataValue(patientId, key) {
    const patientData = this.getPatientData(patientId)
    return patientData[key]
  }

  /**
   * 清理病人数据（当病人页面关闭时调用）
   * @param {string} patientId 病人ID
   */
  clearPatientData(patientId) {
    // 清理EventBus监听器
    const eventBus = this.patientEventBusMap.get(patientId)
    if (eventBus) {
      eventBus.$off() // 移除所有监听器
      this.patientEventBusMap.delete(patientId)
    }

    // 清理数据
    this.patientDataMap.delete(patientId)
  }

  /**
   * 获取所有已缓存的病人ID列表
   * @returns {Array} 病人ID数组
   */
  getCachedPatientIds() {
    return Array.from(this.patientDataMap.keys())
  }

  /**
   * 检查病人数据是否已加载
   * @param {string} patientId 病人ID
   * @returns {boolean} 是否已加载
   */
  isPatientDataLoaded(patientId) {
    const patientData = this.patientDataMap.get(patientId)
    return patientData ? patientData.isLoaded : false
  }

  /**
   * 检查病人数据是否正在加载
   * @param {string} patientId 病人ID
   * @returns {boolean} 是否正在加载
   */
  isPatientDataLoading(patientId) {
    const patientData = this.patientDataMap.get(patientId)
    return patientData ? patientData.isLoading : false
  }
}

// 创建单例实例
const patientDataManager = new PatientDataManager()

export default patientDataManager
