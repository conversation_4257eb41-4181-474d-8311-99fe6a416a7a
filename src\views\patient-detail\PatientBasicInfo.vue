<template>
  <div class="patient-basic-info">
    <div class="patient-basic-title">
      <span />
      病人基础信息
    </div>
    <table>
      <tbody>
        <tr>
          <td class="info-label">患者名称:</td>
          <td class="info-value">{{ patientDetail.bingRenXM }}</td>
          <td class="info-label">性别:</td>
          <td class="info-value">{{ patientDetail.xingBieMC }}</td>
          <td class="info-label">出生日期:</td>
          <td class="info-value">{{ patientDetail.chuShengRQ }}, {{ patientDetail.nianLing }}</td>
          <td class="info-label">联系电话:</td>
          <td class="info-value">{{ patientDetail.lianXiDH }}</td>
        </tr>
        <tr>
          <td class="info-label">病案号:</td>
          <td class="info-value">{{ patientDetail.bingAnHao }}</td>
          <td class="info-label">专科:</td>
          <td class="info-value">{{ patientDetail.zhuanKeMC }}</td>
          <td class="info-label">病区:</td>
          <td class="info-value">{{ patientDetail.bingQuMC }}</td>
          <td class="info-label">床位号:</td>
          <td class="info-value">{{ patientDetail.chuangWeiHao }}</td>
        </tr>
        <tr>
          <td class="info-label">当前治疗组:</td>
          <td class="info-value">
            <a @click="zhiLiaoZuVisible = true">
              {{ patientDetail.zhiLiaoZuMC }}
            </a>
          </td>
          <td class="info-label">主管医师:</td>
          <td class="info-value">
            <a @click="zhuGuanYSVisible = true">{{ patientDetail.zhuGuanYSXM }}</a>
          </td>
          <td class="info-label">护理级别:</td>
          <td class="info-value">
            <a @click="huLiVisible = true">{{ patientDetail.huLiJBMC }}</a>
          </td>
          <td class="info-label">饮食医嘱:</td>
          <td class="info-value">{{ patientDetail.bingRenYS }}</td>
        </tr>
        <tr>
          <td class="info-label">临床诊断:</td>
          <td class="info-value" colspan="7">{{ patientDetail.linChuangZD }}</td>
        </tr>
        <tr>
          <td class="info-label">开住院单医师:</td>
          <td class="info-value">{{ patientDetail.zhuYuanDanYSXM }}</td>
          <td class="info-label">入院单诊断:</td>
          <td class="info-value" colspan="5">
            <a @click="ruYuanVisible = true">{{ patientDetail.ruYuanZD }}</a>
          </td>
        </tr>
        <tr>
          <td class="info-label">财务入院时间:</td>
          <td class="info-value">{{ patientDetail.caiWuRuYuanSJ }}</td>
          <td class="info-label">病区入院时间:</td>
          <td class="info-value">{{ patientDetail.bingQuRuYuanSJ }}</td>
          <td class="info-label">医嘱出院时间:</td>
          <td class="info-value">{{ patientDetail.chuYuanSJ }}</td>
          <td class="info-label">财务出院时间:</td>
          <td class="info-value">{{ patientDetail.jiHuaCYSJ }}</td>
        </tr>
        <tr>
          <td class="info-label">住院天数:</td>
          <td class="info-value">{{ patientDetail.zhuYuanSC }}</td>
          <td class="info-label">预缴金额:</td>
          <td class="info-value">{{ patientDetail.ziFuYJK }}</td>
          <td class="info-label">总记账金额:</td>
          <td class="info-value">{{ patientDetail.zongJiZhangJE }}</td>
          <td class="info-label">总自付金额:</td>
          <td class="info-value">{{ patientDetail.zongZiFuJE }}</td>
        </tr>
        <tr>
          <td class="info-label">其他:</td>
          <td class="info-value" colspan="7">
            <el-button type="primary">药物警戒系统</el-button>
            <el-button type="primary">透析记录单</el-button>
            <el-button type="primary">DRG分组预测</el-button>
            <el-button type="primary">化验单打印</el-button>
          </td>
        </tr>
      </tbody>
    </table>
    <zhi-liao-zu-drawer
      :visible.sync="zhiLiaoZuVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhi-liao-zu-i-d="patientDetail.zhiLiaoZuID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <hu-li-drawer
      :visible.sync="huLiVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :hu-li-j-b-m-c="patientDetail.huLiJBMC"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <zhu-guan-y-s-drawer
      :visible.sync="zhuGuanYSVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhuan-ke-i-d="patientDetail.zhuanKeID"
      :zhu-guan-y-s-i-d="patientDetail.zhuGuanYSID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <el-dialog :visible.sync="ruYuanVisible" width="800px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-menu"></i>
          病人住院单
        </span>
      </span>
      <span>
        <!-- 内容 -->
        <img src="@/assets/test/zhuyuandanTest.png" />
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { PatientInit } from '@/api/patient-info'
import { mapState } from 'vuex'
import ZhiLiaoZuDrawer from '@/views/patient-detail/components/ZhiLiaoZuDrawer.vue'
import HuLiDrawer from '@/views/patient-detail/components/HuLiDrawer.vue'
import ZhuGuanYSDrawer from '@/views/patient-detail/components/ZhuGuanYSDrawer.vue'

export default {
  name: 'PatientBasicInfo',
  components: {
    ZhiLiaoZuDrawer,
    HuLiDrawer,
    ZhuGuanYSDrawer
  },
  data() {
    return {
      zhiLiaoZuVisible: false,
      huLiVisible: false,
      zhuGuanYSVisible: false,

      ruYuanVisible: false
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  methods: {
    // async patientInit() {
    //   PatientInit(this.bingLiID).then((res) => {
    //     if (res.hasError === 0) {
    //       const data = {}
    //       Object.keys(res.data).forEach((key) => {
    //         data[key] = res.data[key] || '─'
    //       })
    //       this.patientDetail = data
    //       console.log('test:----', res.data)
    //     }
    //   })
    // }
  }
}
</script>

<style scoped lang="scss">
.patient-basic-info {
  border-top: none;
  margin: 10px;
  font-size: 13px;
  .patient-basic-title {
    font-weight: 600;
    margin-left: 10px;
    span {
      border-left: 3px solid #356ac5;
      padding-left: 5px;
    }
  }
  table {
    margin-top: 5px;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 10px;
  }
  .info-label {
    text-align: right;
    width: 7vw;
    background-color: #eaf0f9;
  }
  .info-value {
    width: 13vw;
    background-color: #f7f9fd;
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}
</style>
