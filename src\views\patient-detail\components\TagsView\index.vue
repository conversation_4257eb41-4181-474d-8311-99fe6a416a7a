<template>
  <div ref="tagsViewContainer" class="tags-view-container">
    <el-tabs
      v-model="activeRoute"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.name"
        :name="tag.name"
        :label="tag.title"
        :closable="!tag.meta?.affix"
      >
        <component :is="tag.component" :key="`${tag.name}-${refreshKey}`" />
      </el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="initTags">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import patientEventBus from '@/utils/patient-event-bus'
import { isNumberStr } from 'wyyy-component/src/utils'
import { mapState } from 'vuex'

export default {
  name: 'PatientTagView',
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      refreshKey: 0
    }
  },
  computed: {
    ...mapState('patientDetail', ['activeRoute', 'visitedViews', 'currentPatientId']),
    bingLiID() {
      return this.$route.params.id
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    activeRoute(val) {
      // 使用病人专用的EventBus
      patientEventBus.emit('activeRoute', val)
    },
    bingLiID: {
      immediate: true,
      async handler(newId, oldId) {
        if (newId && newId !== oldId) {
          // 切换到新病人
          await this.$store.dispatch('patientDetail/switchToPatient', newId)
          this.initTags()
        }
      }
    }
  },
  mounted() {
    // 监听侧边栏点击事件
    patientEventBus.on('sidebarClick', (menu) => {
      this.addTags(menu)
    })
  },
  destroyed() {
    // 清理事件监听
    patientEventBus.off('sidebarClick')
  },
  methods: {
    initTags() {
      // 使用store的initTags方法
      this.$store.dispatch('patientDetail/initTags')
    },
    addTags(menu) {
      // 使用store的addVisitedView方法
      this.$store.dispatch('patientDetail/addVisitedView', menu)
      this.$store.dispatch('patientDetail/setActiveRoute', menu.name)
    },
    refreshSelectedTag() {
      this.refreshKey += 1
    },
    closeSelectedTag(name) {
      // 使用store的delVisitedView方法
      this.$store.dispatch('patientDetail/delVisitedView', name)
    },
    closeOthersTags() {
      // 使用store的closeOthersTags方法
      this.$store.dispatch('patientDetail/closeOthersTags', this.selectedTag.name)
    },
    // 跳转到最后一个标签
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.activeRoute = latestView.name
      } else {
        this.activeRoute = 'InPatientOrders'
      }
    },
    openMenu(e) {
      console.log(e.target.id)
      if (
        e.srcElement.id &&
        e.srcElement.id.split('tab-')[1] &&
        !isNumberStr(e.srcElement.id.split('tab-')[1])
      ) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        console.log(e.srcElement.id)
        let id = e.srcElement.id.split('tab-')[1]
        this.selectedTag = this.visitedViews.find((f) => f.name === id)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: calc(100% - 93px);
  ::v-deep .el-tabs {
    height: 100%;
    .el-tabs__header {
      background: #fff;
      .el-tabs__nav {
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
        }
      }
    }
    .el-tabs__content {
      height: calc(100% - 32px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
