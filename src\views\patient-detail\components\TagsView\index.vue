<template>
  <div ref="tagsViewContainer" class="tags-view-container">
    <el-tabs
      v-model="activeRoute"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.name"
        :name="tag.name"
        :label="tag.title"
        :closable="!tag.meta?.affix"
      >
        <component :is="tag.component" :key="`${tag.name}-${refreshKey}`" />
      </el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="initTags">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import { loadView } from '@/store/modules/permission'
import { EventBus } from '@/utils/event-bus'
import { isNumberStr } from 'wyyy-component/src/utils'

export default {
  name: 'PatientTagView',
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      activeRoute: '',
      visitedViews: [],
      refreshKey: 0
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    activeRoute(val) {
      EventBus.$emit('activeRoute', val)
    }
  },
  mounted() {
    this.initTags()
    EventBus.$on('sidebarClick', (menu) => {
      this.addTags(menu)
    })
  },
  destroyed() {
    EventBus.$off('sidebarClick')
  },
  methods: {
    initTags() {
      const tag = {
        name: 'InPatientOrders',
        component: loadView('patient-inside/inpatient-orders-menu/InPatientOrders'),
        meta: {
          title: '住院医嘱',
          affix: true
        },
        caiDanLX: 'C'
      }
      this.visitedViews.push(
        Object.assign({}, tag, {
          title: tag.meta.title || 'no-name'
        })
      )
      this.activeRoute = tag.name
    },
    addTags(menu) {
      if (!this.visitedViews.some((v) => v.name === menu.name)) {
        this.visitedViews.push(
          Object.assign({}, menu, {
            title: menu.meta.title || 'no-name'
          })
        )
      }
      this.activeRoute = menu.name
    },
    refreshSelectedTag() {
      this.refreshKey += 1
    },
    closeSelectedTag(name) {
      const index = this.visitedViews.findIndex((v) => v.name === name)
      this.visitedViews.splice(index, 1)
      if (this.activeRoute === name) {
        this.toLastView(this.visitedViews)
      }
    },
    closeOthersTags() {
      this.visitedViews = this.visitedViews.filter(
        (v) => v.meta.affix || v.name === this.selectedTag.name
      )
    },
    // 跳转到最后一个标签
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.activeRoute = latestView.name
      } else {
        this.activeRoute = 'InPatientOrders'
      }
    },
    openMenu(e) {
      console.log(e.target.id)
      if (
        e.srcElement.id &&
        e.srcElement.id.split('tab-')[1] &&
        !isNumberStr(e.srcElement.id.split('tab-')[1])
      ) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        console.log(e.srcElement.id)
        let id = e.srcElement.id.split('tab-')[1]
        this.selectedTag = this.visitedViews.find((f) => f.name === id)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: calc(100% - 93px);
  ::v-deep .el-tabs {
    height: 100%;
    .el-tabs__header {
      background: #fff;
      .el-tabs__nav {
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
        }
      }
    }
    .el-tabs__content {
      height: calc(100% - 32px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
