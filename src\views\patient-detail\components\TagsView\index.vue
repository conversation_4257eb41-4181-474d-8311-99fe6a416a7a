<template>
  <div ref="tagsViewContainer" class="tags-view-container">
    <el-tabs
      v-model="activeRoute"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.name"
        :name="tag.name"
        :label="tag.title"
        :closable="!tag.meta?.affix"
      >
        <component :is="tag.component" :key="`${tag.name}-${refreshKey}`" />
      </el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="initTags">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import { loadView } from '@/store/modules/permission'
import { EventBus } from '@/utils/event-bus'
import simplePatientManager from '@/utils/simple-patient-manager'
import { isNumberStr } from 'wyyy-component/src/utils'

export default {
  name: 'PatientTagView',
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      activeRoute: '',
      visitedViews: [],
      refreshKey: 0
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    activeRoute(val) {
      EventBus.$emit('activeRoute', val)
    }
  },
  mounted() {
    this.restoreOrInitTags()
    EventBus.$on('sidebarClick', (menu) => {
      this.addTags(menu)
    })
  },
  beforeDestroy() {
    // 保存当前状态
    this.saveCurrentState()
    EventBus.$off('sidebarClick')
  },
  methods: {
    // 恢复或初始化标签页
    restoreOrInitTags() {
      if (!this.bingLiID) return

      const savedState = simplePatientManager.getPatientTabsState(this.bingLiID)

      if (savedState.visitedViews.length > 0) {
        // 恢复保存的状态
        console.log(`恢复病人 ${this.bingLiID} 的标签页状态`)
        this.visitedViews = [...savedState.visitedViews]
        this.activeRoute = savedState.activeRoute
      } else {
        // 初始化默认标签
        console.log(`初始化病人 ${this.bingLiID} 的默认标签`)
        this.initDefaultTags()
      }
    },

    // 初始化默认标签
    initDefaultTags() {
      const tag = {
        name: 'InPatientOrders',
        component: loadView('patient-inside/inpatient-orders-menu/InPatientOrders'),
        meta: {
          title: '住院医嘱',
          affix: true
        },
        caiDanLX: 'C'
      }
      const newView = Object.assign({}, tag, {
        title: tag.meta.title || 'no-name'
      })

      this.visitedViews = [newView]
      this.activeRoute = tag.name

      // 保存初始状态
      this.saveCurrentState()
    },

    // 保存当前状态
    saveCurrentState() {
      if (!this.bingLiID) return

      simplePatientManager.savePatientTabsState(this.bingLiID, {
        visitedViews: [...this.visitedViews],
        activeRoute: this.activeRoute
      })
    },
    addTags(menu) {
      if (!this.visitedViews.some((v) => v.name === menu.name)) {
        this.visitedViews.push(
          Object.assign({}, menu, {
            title: menu.meta.title || 'no-name'
          })
        )
      }
      this.activeRoute = menu.name

      // 保存状态
      this.saveCurrentState()
    },
    refreshSelectedTag() {
      this.refreshKey += 1
    },
    closeSelectedTag(name) {
      const index = this.visitedViews.findIndex((v) => v.name === name)
      this.visitedViews.splice(index, 1)
      if (this.activeRoute === name) {
        this.toLastView(this.visitedViews)
      }

      // 保存状态
      this.saveCurrentState()
    },
    closeOthersTags() {
      this.visitedViews = this.visitedViews.filter(
        (v) => v.meta.affix || v.name === this.selectedTag.name
      )

      // 保存状态
      this.saveCurrentState()
    },
    // 跳转到最后一个标签
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.activeRoute = latestView.name
      } else {
        this.activeRoute = 'InPatientOrders'
      }
    },
    openMenu(e) {
      console.log(e.target.id)
      if (
        e.srcElement.id &&
        e.srcElement.id.split('tab-')[1] &&
        !isNumberStr(e.srcElement.id.split('tab-')[1])
      ) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        console.log(e.srcElement.id)
        let id = e.srcElement.id.split('tab-')[1]
        this.selectedTag = this.visitedViews.find((f) => f.name === id)
      }
    },
    closeMenu() {
      this.visible = false
    },

    // 兼容原有的initTags方法调用
    initTags() {
      this.restoreOrInitTags()
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: calc(100% - 93px);
  ::v-deep .el-tabs {
    height: 100%;
    .el-tabs__header {
      background: #fff;
      .el-tabs__nav {
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
        }
      }
    }
    .el-tabs__content {
      height: calc(100% - 32px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
