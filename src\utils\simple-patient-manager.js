/**
 * 简化的病人数据管理器
 * 基于现有的 keep-alive 和标签页系统，提供最小化的数据隔离
 */

class SimplePatientManager {
  constructor() {
    // 存储每个病人的二级标签页状态
    this.patientTabsState = new Map()
  }

  /**
   * 获取病人的标签页状态
   * @param {string} patientId 病人ID
   * @returns {Object} 标签页状态
   */
  getPatientTabsState(patientId) {
    if (!this.patientTabsState.has(patientId)) {
      this.patientTabsState.set(patientId, {
        visitedViews: [],
        activeRoute: '',
        initialized: false
      })
    }
    return this.patientTabsState.get(patientId)
  }

  /**
   * 保存病人的标签页状态
   * @param {string} patientId 病人ID
   * @param {Object} state 状态对象
   */
  savePatientTabsState(patientId, state) {
    const currentState = this.getPatientTabsState(patientId)
    Object.assign(currentState, state)
  }

  /**
   * 清理病人数据（当主标签页关闭时）
   * @param {string} patientId 病人ID
   */
  clearPatientData(patientId) {
    this.patientTabsState.delete(patientId)
    console.log(`清理病人 ${patientId} 的数据`)
  }

  /**
   * 检查病人是否已初始化
   * @param {string} patientId 病人ID
   * @returns {boolean} 是否已初始化
   */
  isPatientInitialized(patientId) {
    const state = this.patientTabsState.get(patientId)
    return state ? state.initialized : false
  }

  /**
   * 标记病人为已初始化
   * @param {string} patientId 病人ID
   */
  markPatientInitialized(patientId) {
    const state = this.getPatientTabsState(patientId)
    state.initialized = true
  }

  /**
   * 获取所有缓存的病人ID
   * @returns {Array} 病人ID数组
   */
  getCachedPatientIds() {
    return Array.from(this.patientTabsState.keys())
  }

  /**
   * 获取调试信息
   * @returns {Object} 调试信息
   */
  getDebugInfo() {
    const debugInfo = {}
    for (const [patientId, state] of this.patientTabsState.entries()) {
      debugInfo[patientId] = {
        visitedViewsCount: state.visitedViews.length,
        activeRoute: state.activeRoute,
        initialized: state.initialized
      }
    }
    return debugInfo
  }
}

// 创建单例实例
const simplePatientManager = new SimplePatientManager()

// 监听主标签页关闭事件
if (typeof window !== 'undefined') {
  // 监听 tagsView store 的变化，当病人标签页被删除时清理数据
  let store
  try {
    store = require('@/store').default
    
    // 监听 tagsView 的 visitedViews 变化
    store.watch(
      (state) => state.tagsView.visitedViews,
      (newViews, oldViews) => {
        if (oldViews && oldViews.length > newViews.length) {
          // 找出被删除的标签页
          const deletedViews = oldViews.filter(oldView => 
            !newViews.some(newView => newView.fullPath === oldView.fullPath)
          )
          
          // 清理被删除的病人数据
          deletedViews.forEach(view => {
            if (view.path && view.path.startsWith('/patient-detail/')) {
              const patientId = view.path.split('/patient-detail/')[1]
              if (patientId) {
                simplePatientManager.clearPatientData(patientId)
              }
            }
          })
        }
      }
    )
  } catch (error) {
    console.warn('无法监听 tagsView 变化:', error)
  }
}

export default simplePatientManager
