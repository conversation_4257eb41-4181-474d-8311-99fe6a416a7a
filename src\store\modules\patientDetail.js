import patientDataManager from '@/utils/patient-data-manager'
import { loadView } from '@/store/modules/permission'

// 病人详情动态模块
const patientDetailModule = {
  namespaced: true,
  state: () => ({
    currentPatientId: null, // 当前活跃的病人ID
    patientInit: {}, // 病人初始化数据
    activeMenuItem: null, // 当前激活的菜单项
    visitedViews: [], // 添加visitedViews数组
    activeRoute: '', // 当前激活的路由
    isLoading: false, // 添加加载状态标志
    isLoaded: false // 添加已加载标志
  }),
  mutations: {
    SET_CURRENT_PATIENT_ID(state, patientId) {
      state.currentPatientId = patientId
      patientDataManager.setCurrentPatient(patientId)
    },
    SET_PATIENT_INIT(state, data) {
      state.patientInit = data
      // 同步到病人数据管理器
      if (state.currentPatientId) {
        patientDataManager.updatePatientData(state.currentPatientId, 'patientInit', data)
      }
    },
    SET_ACTIVE_MENU_ITEM(state, item) {
      state.activeMenuItem = item
    },
    // 添加管理visitedViews的mutations
    ADD_VISITED_VIEW(state, view) {
      if (state.visitedViews.some((v) => v.name === view.name)) return
      const newView = Object.assign({}, view, {
        title: view.meta.title || 'no-name'
      })
      state.visitedViews.push(newView)

      // 同步到病人数据管理器
      if (state.currentPatientId) {
        const patientData = patientDataManager.getPatientData(state.currentPatientId)
        patientData.visitedViews = [...state.visitedViews]
      }
    },
    DEL_VISITED_VIEW(state, name) {
      const index = state.visitedViews.findIndex((v) => v.name === name)
      if (index > -1) {
        state.visitedViews.splice(index, 1)

        // 同步到病人数据管理器
        if (state.currentPatientId) {
          const patientData = patientDataManager.getPatientData(state.currentPatientId)
          patientData.visitedViews = [...state.visitedViews]
        }
      }
    },
    SET_ACTIVE_ROUTE(state, route) {
      state.activeRoute = route
      // 同步到病人数据管理器
      if (state.currentPatientId) {
        patientDataManager.updatePatientData(state.currentPatientId, 'activeRoute', route)
      }
    },
    FILTER_VISITED_VIEWS(state, name) {
      state.visitedViews = state.visitedViews.filter((v) => v.meta.affix || v.name === name)
      // 同步到病人数据管理器
      if (state.currentPatientId) {
        const patientData = patientDataManager.getPatientData(state.currentPatientId)
        patientData.visitedViews = [...state.visitedViews]
      }
    },
    RESTORE_PATIENT_STATE(state, patientId) {
      const patientData = patientDataManager.getPatientData(patientId)

      state.currentPatientId = patientId
      state.activeRoute = patientData.activeRoute || ''
      state.visitedViews = patientData.visitedViews || []
      state.isLoading = patientData.isLoading || false
      state.isLoaded = patientData.isLoaded || false
      state.patientInit = patientData.patientInit || {}

      // 如果没有访问过的视图，初始化默认标签
      if (state.visitedViews.length === 0) {
        const defaultTag = {
          name: 'InPatientOrders',
          component: loadView('patient-inside/inpatient-orders-menu/InPatientOrders'),
          meta: {
            title: '住院医嘱',
            affix: true
          },
          caiDanLX: 'C'
        }

        state.visitedViews.push(
          Object.assign({}, defaultTag, {
            title: defaultTag.meta.title || 'no-name'
          })
        )

        state.activeRoute = defaultTag.name

        // 同步到病人数据管理器
        patientData.visitedViews = [...state.visitedViews]
        patientData.activeRoute = state.activeRoute
      }
    },
    INIT_TAGS(state) {
      // 初始化标签，只保留固定标签
      state.visitedViews = state.visitedViews.filter((v) => v.meta && v.meta.affix)
      // 如果没有标签，添加默认标签
      if (state.visitedViews.length === 0) {
        const tag = {
          name: 'InPatientOrders',
          component: null, // 组件会在TagsView中动态加载
          meta: {
            title: '住院医嘱',
            affix: true
          },
          caiDanLX: 'C'
        }
        state.visitedViews.push(
          Object.assign({}, tag, {
            title: tag.meta.title || 'no-name'
          })
        )
      }
      // 设置默认激活路由
      if (state.visitedViews.length > 0) {
        state.activeRoute = state.visitedViews[0].name
      }

      // 同步到病人数据管理器
      if (state.currentPatientId) {
        const patientData = patientDataManager.getPatientData(state.currentPatientId)
        patientData.visitedViews = [...state.visitedViews]
        patientData.activeRoute = state.activeRoute
      }
    },
    SET_LOADING(state, status) {
      state.isLoading = status
      if (state.currentPatientId) {
        patientDataManager.updatePatientData(state.currentPatientId, 'isLoading', status)
      }
    },
    SET_LOADED(state, status) {
      state.isLoaded = status
      if (state.currentPatientId) {
        patientDataManager.updatePatientData(state.currentPatientId, 'isLoaded', status)
      }
    }
  },
  actions: {
    // 切换到指定病人
    async switchToPatient({ commit, dispatch }, patientId) {
      commit('SET_CURRENT_PATIENT_ID', patientId)
      commit('RESTORE_PATIENT_STATE', patientId)

      // 如果病人数据未加载，则初始化
      if (!patientDataManager.isPatientDataLoaded(patientId)) {
        await dispatch('getPatientInit', patientId)
      }
    },

    async getPatientInit({ commit, state }, bingLiID) {
      // 检查是否为当前病人，如果不是则切换
      if (state.currentPatientId !== bingLiID) {
        commit('SET_CURRENT_PATIENT_ID', bingLiID)
      }

      // 检查缓存中是否已有数据
      const cachedData = patientDataManager.getPatientDataValue(bingLiID, 'patientInit')
      const isLoaded = patientDataManager.isPatientDataLoaded(bingLiID)
      const isLoading = patientDataManager.isPatientDataLoading(bingLiID)

      // 如果已经在加载，直接返回当前状态
      if (isLoading) {
        console.log('已经在加载中，不重复请求')
        return cachedData
      }

      // 如果已经加载完成且有数据，直接返回
      if (isLoaded && cachedData?.bingRenBH) {
        console.log('已经加载完成，使用缓存数据')
        commit('SET_PATIENT_INIT', cachedData)
        commit('SET_LOADED', true)
        return cachedData
      }

      // 设置加载状态
      commit('SET_LOADING', true)
      commit('SET_LOADED', false)

      try {
        // 使用病人数据管理器初始化数据
        const patientInit = await patientDataManager.initPatientData(bingLiID)

        if (patientInit && Object.keys(patientInit).length > 0) {
          commit('SET_PATIENT_INIT', patientInit)
          commit('SET_LOADED', true)
          return { hasError: 0, data: patientInit }
        } else {
          console.warn('初始化病人数据失败或数据为空')
          commit('SET_LOADED', true)
          return { hasError: 1, message: '初始化病人数据失败' }
        }
      } catch (error) {
        console.error('加载病人数据过程中发生错误:', error)
        commit('SET_LOADED', true)
        return { hasError: 1, error, message: '加载病人数据失败' }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    setActiveMenuItem({ commit }, item) {
      commit('SET_ACTIVE_MENU_ITEM', item)
      // 同时添加到visitedViews
      commit('ADD_VISITED_VIEW', item)
      commit('SET_ACTIVE_ROUTE', item.name)
    },
    addVisitedView({ commit }, view) {
      commit('ADD_VISITED_VIEW', view)
    },
    delVisitedView({ commit, state }, name) {
      commit('DEL_VISITED_VIEW', name)
      // 如果删除的是当前激活的路由，需要切换到最后一个标签
      if (state.activeRoute === name) {
        const latestView = state.visitedViews.slice(-1)[0]
        if (latestView) {
          commit('SET_ACTIVE_ROUTE', latestView.name)
        } else {
          commit('SET_ACTIVE_ROUTE', 'InPatientOrders')
        }
      }
      return state.visitedViews
    },
    setActiveRoute({ commit }, route) {
      commit('SET_ACTIVE_ROUTE', route)
    },
    closeOthersTags({ commit }, name) {
      commit('FILTER_VISITED_VIEWS', name)
    },
    initTags({ commit }) {
      commit('INIT_TAGS')
    },
    // 清理病人数据
    clearPatientData(_, patientId) {
      patientDataManager.clearPatientData(patientId)
    }
  },
  getters: {
    // 获取当前病人的数据
    currentPatientData: (state) => {
      if (!state.currentPatientId) return {}
      return patientDataManager.getPatientData(state.currentPatientId)
    },

    // 获取当前病人的初始化数据
    currentPatientInit: (state) => {
      if (!state.currentPatientId) return {}
      const patientData = patientDataManager.getPatientData(state.currentPatientId)
      return patientData.patientInit || state.patientInit || {}
    },

    // 检查当前病人数据是否已加载
    isCurrentPatientLoaded: (state) => {
      if (!state.currentPatientId) return false
      return patientDataManager.isPatientDataLoaded(state.currentPatientId)
    },

    // 检查当前病人数据是否正在加载
    isCurrentPatientLoading: (state) => {
      if (!state.currentPatientId) return false
      return patientDataManager.isPatientDataLoading(state.currentPatientId)
    }
  }
}

export default patientDetailModule
