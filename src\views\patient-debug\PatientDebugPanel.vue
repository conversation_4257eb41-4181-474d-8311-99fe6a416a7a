<!--
  病人数据管理调试面板
  用于测试和调试病人数据管理系统
-->
<template>
  <div class="patient-debug-panel">
    <el-card>
      <div slot="header">
        <span>病人数据管理调试面板</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新数据
        </el-button>
      </div>

      <!-- 系统状态 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="系统状态" name="systemState">
          <div class="debug-section">
            <p><strong>当前病人ID：</strong>{{ systemState.currentPatientId || '无' }}</p>
            <p><strong>缓存病人数量：</strong>{{ systemState.cachedPatientsCount }}</p>
            <p><strong>缓存病人列表：</strong>{{ systemState.cachedPatientIds.join(', ') || '无' }}</p>
            
            <h4>Store状态：</h4>
            <ul>
              <li>当前病人ID: {{ systemState.storeState.currentPatientId }}</li>
              <li>激活路由: {{ systemState.storeState.activeRoute }}</li>
              <li>标签页数量: {{ systemState.storeState.visitedViewsCount }}</li>
              <li>加载状态: {{ systemState.storeState.isLoading ? '加载中' : '未加载' }}</li>
              <li>已加载: {{ systemState.storeState.isLoaded ? '是' : '否' }}</li>
            </ul>
          </div>
        </el-collapse-item>

        <!-- 数据一致性检查 -->
        <el-collapse-item title="数据一致性检查" name="consistency">
          <div class="debug-section">
            <el-alert
              :title="consistencyCheck.isConsistent ? '数据一致' : '发现数据不一致'"
              :type="consistencyCheck.isConsistent ? 'success' : 'error'"
              show-icon
            />
            
            <div v-if="!consistencyCheck.isConsistent" class="consistency-issues">
              <h4>发现的问题：</h4>
              <ul>
                <li v-for="issue in consistencyCheck.issues" :key="issue.type" class="issue-item">
                  <strong>{{ issue.type }}:</strong> {{ issue.message }}
                </li>
              </ul>
            </div>
            
            <p><small>检查时间: {{ consistencyCheck.checkedAt }}</small></p>
          </div>
        </el-collapse-item>

        <!-- 病人详情 -->
        <el-collapse-item title="病人详情" name="patientDetails">
          <div class="debug-section">
            <el-select v-model="selectedPatientId" placeholder="选择病人" style="margin-bottom: 10px;">
              <el-option
                v-for="patientId in systemState.cachedPatientIds"
                :key="patientId"
                :label="patientId"
                :value="patientId"
              />
            </el-select>
            
            <div v-if="selectedPatientDetails">
              <h4>病人 {{ selectedPatientDetails.patientId }} 详情：</h4>
              <ul>
                <li>数据已加载: {{ selectedPatientDetails.isLoaded ? '是' : '否' }}</li>
                <li>正在加载: {{ selectedPatientDetails.isLoading ? '是' : '否' }}</li>
                <li>有初始化数据: {{ selectedPatientDetails.patientData.hasPatientInit ? '是' : '否' }}</li>
                <li>初始化数据字段: {{ selectedPatientDetails.patientData.patientInitKeys.join(', ') }}</li>
                <li>激活路由: {{ selectedPatientDetails.patientData.activeRoute || '无' }}</li>
                <li>标签页数量: {{ selectedPatientDetails.patientData.visitedViewsCount }}</li>
                <li>业务数据字段: {{ selectedPatientDetails.patientData.businessDataKeys.join(', ') || '无' }}</li>
              </ul>
              
              <h5>标签页列表：</h5>
              <ul>
                <li v-for="view in selectedPatientDetails.patientData.visitedViews" :key="view.name">
                  {{ view.name }} - {{ view.title }}
                </li>
              </ul>
            </div>
          </div>
        </el-collapse-item>

        <!-- 调试日志 -->
        <el-collapse-item title="调试日志" name="logs">
          <div class="debug-section">
            <el-button size="small" @click="clearLogs">清空日志</el-button>
            <div class="logs-container">
              <div v-for="log in recentLogs" :key="log.timestamp" class="log-entry" :class="log.type">
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-type">[{{ log.type }}]</span>
                <span class="log-message">{{ log.message }}</span>
                <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-collapse-item>

        <!-- 操作工具 -->
        <el-collapse-item title="操作工具" name="tools">
          <div class="debug-section">
            <el-button @click="forceRefreshCurrentPatient">强制刷新当前病人数据</el-button>
            <el-button @click="exportDebugReport">导出调试报告</el-button>
            <el-button @click="simulatePatientSwitch">模拟病人切换</el-button>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script>
import patientDebugHelper from '@/utils/patient-debug-helper'

export default {
  name: 'PatientDebugPanel',
  
  data() {
    return {
      activeCollapse: ['systemState'],
      systemState: {},
      consistencyCheck: {},
      selectedPatientId: '',
      selectedPatientDetails: null,
      recentLogs: []
    }
  },

  watch: {
    selectedPatientId(newId) {
      if (newId) {
        this.selectedPatientDetails = patientDebugHelper.getPatientDetails(newId)
      }
    }
  },

  mounted() {
    this.refreshData()
    // 定期刷新数据
    this.refreshTimer = setInterval(this.refreshData, 5000)
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  methods: {
    refreshData() {
      this.systemState = patientDebugHelper.getSystemState()
      this.consistencyCheck = patientDebugHelper.checkDataConsistency()
      this.recentLogs = patientDebugHelper.getRecentLogs(20)
      
      // 如果有选中的病人，更新其详情
      if (this.selectedPatientId) {
        this.selectedPatientDetails = patientDebugHelper.getPatientDetails(this.selectedPatientId)
      }
    },

    clearLogs() {
      patientDebugHelper.clearLogs()
      this.recentLogs = []
    },

    async forceRefreshCurrentPatient() {
      await patientDebugHelper.forceRefreshPatient()
      this.refreshData()
    },

    exportDebugReport() {
      const report = patientDebugHelper.exportDebugReport()
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `patient-debug-report-${new Date().toISOString().slice(0, 19)}.json`
      a.click()
      URL.revokeObjectURL(url)
    },

    simulatePatientSwitch() {
      // 模拟切换到不同的病人
      const patientIds = ['patient-001', 'patient-002', 'patient-003']
      const randomId = patientIds[Math.floor(Math.random() * patientIds.length)]
      this.$store.dispatch('patientDetail/switchToPatient', randomId)
      this.$message.info(`模拟切换到病人: ${randomId}`)
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.patient-debug-panel {
  padding: 20px;
}

.debug-section {
  padding: 10px 0;
}

.consistency-issues {
  margin-top: 10px;
}

.issue-item {
  color: #f56c6c;
  margin: 5px 0;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  background: #f9f9f9;
}

.log-entry {
  margin-bottom: 10px;
  padding: 5px;
  border-radius: 3px;
}

.log-entry.error {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.log-entry.success {
  background: #f0f9ff;
  border-left: 3px solid #67c23a;
}

.log-entry.info {
  background: #f4f4f5;
  border-left: 3px solid #909399;
}

.log-time {
  color: #999;
  font-size: 12px;
}

.log-type {
  font-weight: bold;
  margin: 0 5px;
}

.log-data {
  background: #f5f5f5;
  padding: 5px;
  margin-top: 5px;
  font-size: 12px;
  border-radius: 3px;
}
</style>
