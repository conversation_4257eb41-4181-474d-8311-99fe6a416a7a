<!--
  简化病人数据管理测试页面
  用于验证简化方案的效果
-->
<template>
  <div class="simple-patient-test">
    <el-card>
      <div slot="header">
        <span>简化病人数据管理测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新
        </el-button>
      </div>

      <!-- 当前状态 -->
      <div class="test-section">
        <h3>当前状态</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never">
              <div slot="header">路由信息</div>
              <p><strong>当前路由：</strong>{{ $route.path }}</p>
              <p><strong>病人ID：</strong>{{ $route.params.id || '无' }}</p>
              <p><strong>组件Key：</strong>{{ componentKey }}</p>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="never">
              <div slot="header">缓存状态</div>
              <p><strong>已缓存病人：</strong>{{ cachedPatients.join(', ') || '无' }}</p>
              <p><strong>当前病人已初始化：</strong>{{ isCurrentPatientInitialized ? '是' : '否' }}</p>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 标签页状态 -->
      <div class="test-section">
        <h3>标签页状态</h3>
        <el-table :data="patientTabsData" border>
          <el-table-column prop="patientId" label="病人ID" width="120" />
          <el-table-column prop="visitedViewsCount" label="标签页数量" width="120" />
          <el-table-column prop="activeRoute" label="激活路由" />
          <el-table-column prop="initialized" label="已初始化" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.initialized ? 'success' : 'info'">
                {{ scope.row.initialized ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作测试 -->
      <div class="test-section">
        <h3>操作测试</h3>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-button @click="simulatePatientSwitch" type="primary">
              模拟病人切换
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="clearPatientData" type="warning">
              清理当前病人数据
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="addTestTab" type="success">
              添加测试标签页
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="exportTestReport" type="info">
              导出测试报告
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- API请求监控 -->
      <div class="test-section">
        <h3>API请求监控</h3>
        <el-alert
          :title="`总请求次数: ${apiRequestCount}`"
          :type="apiRequestCount > expectedRequestCount ? 'error' : 'success'"
          show-icon
        />
        <p><small>期望请求次数: {{ expectedRequestCount }} (每个病人首次访问时请求一次)</small></p>
        
        <div class="request-log">
          <h4>请求日志：</h4>
          <div class="log-container">
            <div v-for="(log, index) in requestLogs" :key="index" class="log-entry">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-patient">病人{{ log.patientId }}</span>
              <span class="log-action">{{ log.action }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-section">
        <h3>测试结果</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never">
              <div slot="header">重复请求测试</div>
              <el-tag :type="duplicateRequestTest.passed ? 'success' : 'danger'">
                {{ duplicateRequestTest.passed ? '通过' : '失败' }}
              </el-tag>
              <p><small>{{ duplicateRequestTest.message }}</small></p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="never">
              <div slot="header">状态保持测试</div>
              <el-tag :type="statePreservationTest.passed ? 'success' : 'danger'">
                {{ statePreservationTest.passed ? '通过' : '失败' }}
              </el-tag>
              <p><small>{{ statePreservationTest.message }}</small></p>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="never">
              <div slot="header">内存清理测试</div>
              <el-tag :type="memoryCleanupTest.passed ? 'success' : 'danger'">
                {{ memoryCleanupTest.passed ? '通过' : '失败' }}
              </el-tag>
              <p><small>{{ memoryCleanupTest.message }}</small></p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import simplePatientManager from '@/utils/simple-patient-manager'

export default {
  name: 'SimplePatientTest',
  
  data() {
    return {
      cachedPatients: [],
      patientTabsData: [],
      apiRequestCount: 0,
      expectedRequestCount: 0,
      requestLogs: [],
      duplicateRequestTest: { passed: true, message: '暂无测试' },
      statePreservationTest: { passed: true, message: '暂无测试' },
      memoryCleanupTest: { passed: true, message: '暂无测试' }
    }
  },

  computed: {
    componentKey() {
      if (this.$route.path.startsWith('/patient-detail/')) {
        return `PatientDetail-${this.$route.params.id}`
      }
      return this.$route.fullPath
    },
    
    isCurrentPatientInitialized() {
      const patientId = this.$route.params.id
      return patientId ? simplePatientManager.isPatientInitialized(patientId) : false
    }
  },

  mounted() {
    this.refreshData()
    this.startApiMonitoring()
    
    // 定期刷新数据
    this.refreshTimer = setInterval(this.refreshData, 3000)
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  methods: {
    refreshData() {
      this.cachedPatients = simplePatientManager.getCachedPatientIds()
      this.updatePatientTabsData()
      this.runTests()
    },

    updatePatientTabsData() {
      const debugInfo = simplePatientManager.getDebugInfo()
      this.patientTabsData = Object.entries(debugInfo).map(([patientId, info]) => ({
        patientId,
        visitedViewsCount: info.visitedViewsCount,
        activeRoute: info.activeRoute || '无',
        initialized: info.initialized
      }))
    },

    startApiMonitoring() {
      // 监控API请求（这里是模拟，实际项目中可以通过拦截器实现）
      const originalDispatch = this.$store.dispatch
      this.$store.dispatch = (...args) => {
        if (args[0] === 'patient/getPatientInit') {
          this.apiRequestCount++
          this.requestLogs.unshift({
            timestamp: new Date(),
            patientId: args[1],
            action: '请求病人数据'
          })
          
          // 只保留最近20条日志
          if (this.requestLogs.length > 20) {
            this.requestLogs = this.requestLogs.slice(0, 20)
          }
        }
        return originalDispatch.apply(this.$store, args)
      }
    },

    simulatePatientSwitch() {
      const patientIds = ['patient-001', 'patient-002', 'patient-003']
      const randomId = patientIds[Math.floor(Math.random() * patientIds.length)]
      
      // 模拟路由跳转
      this.$router.push(`/patient-detail/${randomId}`)
      this.$message.info(`模拟切换到病人: ${randomId}`)
    },

    clearPatientData() {
      const patientId = this.$route.params.id
      if (patientId) {
        simplePatientManager.clearPatientData(patientId)
        this.$message.success(`清理病人 ${patientId} 的数据`)
        this.refreshData()
      }
    },

    addTestTab() {
      // 模拟添加标签页
      const testTab = {
        name: `TestTab-${Date.now()}`,
        meta: { title: '测试标签页' }
      }
      
      // 这里应该触发实际的标签页添加逻辑
      this.$message.info('添加测试标签页')
    },

    exportTestReport() {
      const report = {
        timestamp: new Date().toISOString(),
        componentKey: this.componentKey,
        cachedPatients: this.cachedPatients,
        patientTabsData: this.patientTabsData,
        apiRequestCount: this.apiRequestCount,
        expectedRequestCount: this.expectedRequestCount,
        requestLogs: this.requestLogs,
        testResults: {
          duplicateRequestTest: this.duplicateRequestTest,
          statePreservationTest: this.statePreservationTest,
          memoryCleanupTest: this.memoryCleanupTest
        }
      }

      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `simple-patient-test-report-${new Date().toISOString().slice(0, 19)}.json`
      a.click()
      URL.revokeObjectURL(url)
    },

    runTests() {
      // 重复请求测试
      this.expectedRequestCount = new Set(this.requestLogs.map(log => log.patientId)).size
      this.duplicateRequestTest = {
        passed: this.apiRequestCount <= this.expectedRequestCount * 1.2, // 允许20%的误差
        message: this.apiRequestCount <= this.expectedRequestCount * 1.2 
          ? '没有发现明显的重复请求' 
          : `发现可能的重复请求，实际${this.apiRequestCount}次，期望${this.expectedRequestCount}次`
      }

      // 状态保持测试
      const hasMultiplePatients = this.cachedPatients.length > 1
      this.statePreservationTest = {
        passed: hasMultiplePatients ? this.patientTabsData.some(p => p.visitedViewsCount > 0) : true,
        message: hasMultiplePatients 
          ? '多个病人的状态正常保持' 
          : '需要切换多个病人来测试状态保持'
      }

      // 内存清理测试
      this.memoryCleanupTest = {
        passed: this.cachedPatients.length <= 5, // 假设不应该缓存太多病人
        message: this.cachedPatients.length <= 5 
          ? '内存使用正常' 
          : '缓存的病人数量过多，可能存在内存泄漏'
      }
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.simple-patient-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.request-log {
  margin-top: 15px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  background: #f9f9f9;
}

.log-entry {
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #999;
  margin-right: 10px;
}

.log-patient {
  color: #409eff;
  margin-right: 10px;
}

.log-action {
  color: #333;
}
</style>
