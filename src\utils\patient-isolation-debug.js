/**
 * 病人数据隔离调试工具
 * 用于验证病人间数据隔离是否正常工作
 */

import simplePatientManager from './simple-patient-manager'

class PatientIsolationDebug {
  constructor() {
    this.testResults = []
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 记录测试日志
   * @param {string} type 日志类型
   * @param {string} message 日志消息
   * @param {any} data 附加数据
   */
  log(type, message, data = null) {
    if (!this.isEnabled) return

    const logEntry = {
      timestamp: new Date().toISOString(),
      type,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    }

    console.log(`[PatientIsolation:${type}] ${message}`, data || '')
    this.testResults.unshift(logEntry)
    
    // 只保留最近50条记录
    if (this.testResults.length > 50) {
      this.testResults = this.testResults.slice(0, 50)
    }
  }

  /**
   * 测试EventBus隔离
   * @param {string} patientId1 病人1 ID
   * @param {string} patientId2 病人2 ID
   */
  testEventBusIsolation(patientId1, patientId2) {
    this.log('test', `开始测试EventBus隔离: ${patientId1} vs ${patientId2}`)

    const eventBus1 = simplePatientManager.getPatientEventBus(patientId1)
    const eventBus2 = simplePatientManager.getPatientEventBus(patientId2)

    // 验证是否是不同的实例
    const isDifferentInstance = eventBus1 !== eventBus2
    this.log('result', `EventBus实例隔离: ${isDifferentInstance ? '通过' : '失败'}`, {
      patientId1,
      patientId2,
      eventBus1Id: eventBus1._uid,
      eventBus2Id: eventBus2._uid
    })

    // 测试事件隔离
    let patient1Received = false
    let patient2Received = false

    eventBus1.$on('testEvent', () => {
      patient1Received = true
    })

    eventBus2.$on('testEvent', () => {
      patient2Received = true
    })

    // 只向病人1发送事件
    eventBus1.$emit('testEvent')

    setTimeout(() => {
      const isIsolated = patient1Received && !patient2Received
      this.log('result', `事件隔离测试: ${isIsolated ? '通过' : '失败'}`, {
        patient1Received,
        patient2Received
      })

      // 清理监听器
      eventBus1.$off('testEvent')
      eventBus2.$off('testEvent')
    }, 100)

    return { isDifferentInstance, eventBus1, eventBus2 }
  }

  /**
   * 测试标签页状态隔离
   * @param {string} patientId1 病人1 ID
   * @param {string} patientId2 病人2 ID
   */
  testTabsStateIsolation(patientId1, patientId2) {
    this.log('test', `开始测试标签页状态隔离: ${patientId1} vs ${patientId2}`)

    // 为病人1添加标签页
    const testTab1 = {
      name: 'TestTab1',
      title: '测试标签1',
      meta: { title: '测试标签1' }
    }

    simplePatientManager.savePatientTabsState(patientId1, {
      visitedViews: [testTab1],
      activeRoute: 'TestTab1'
    })

    // 为病人2添加不同的标签页
    const testTab2 = {
      name: 'TestTab2',
      title: '测试标签2',
      meta: { title: '测试标签2' }
    }

    simplePatientManager.savePatientTabsState(patientId2, {
      visitedViews: [testTab2],
      activeRoute: 'TestTab2'
    })

    // 验证状态隔离
    const state1 = simplePatientManager.getPatientTabsState(patientId1)
    const state2 = simplePatientManager.getPatientTabsState(patientId2)

    const isIsolated = 
      state1.visitedViews.length === 1 &&
      state2.visitedViews.length === 1 &&
      state1.visitedViews[0].name === 'TestTab1' &&
      state2.visitedViews[0].name === 'TestTab2' &&
      state1.activeRoute === 'TestTab1' &&
      state2.activeRoute === 'TestTab2'

    this.log('result', `标签页状态隔离: ${isIsolated ? '通过' : '失败'}`, {
      patient1State: {
        visitedViewsCount: state1.visitedViews.length,
        activeRoute: state1.activeRoute,
        firstTabName: state1.visitedViews[0]?.name
      },
      patient2State: {
        visitedViewsCount: state2.visitedViews.length,
        activeRoute: state2.activeRoute,
        firstTabName: state2.visitedViews[0]?.name
      }
    })

    return { isIsolated, state1, state2 }
  }

  /**
   * 模拟真实的标签页添加场景
   * @param {string} patientId 病人ID
   * @param {Object} menuItem 菜单项
   */
  simulateAddTab(patientId, menuItem) {
    this.log('action', `模拟为病人 ${patientId} 添加标签页: ${menuItem.name}`)

    const currentState = simplePatientManager.getPatientTabsState(patientId)
    const newVisitedViews = [...currentState.visitedViews]

    // 检查是否已存在
    if (!newVisitedViews.some(v => v.name === menuItem.name)) {
      newVisitedViews.push({
        ...menuItem,
        title: menuItem.meta?.title || menuItem.name
      })
    }

    simplePatientManager.savePatientTabsState(patientId, {
      visitedViews: newVisitedViews,
      activeRoute: menuItem.name
    })

    this.log('result', `标签页添加完成`, {
      patientId,
      totalTabs: newVisitedViews.length,
      activeRoute: menuItem.name
    })

    return newVisitedViews
  }

  /**
   * 运行完整的隔离测试
   * @param {string} patientId1 病人1 ID
   * @param {string} patientId2 病人2 ID
   */
  runFullIsolationTest(patientId1 = 'test-patient-001', patientId2 = 'test-patient-002') {
    this.log('test', '开始完整的病人数据隔离测试')

    // 清理之前的测试数据
    simplePatientManager.clearPatientData(patientId1)
    simplePatientManager.clearPatientData(patientId2)

    const results = {
      eventBusTest: this.testEventBusIsolation(patientId1, patientId2),
      tabsStateTest: this.testTabsStateIsolation(patientId1, patientId2)
    }

    // 模拟真实场景：病人1添加"病程记录"标签
    this.simulateAddTab(patientId1, {
      name: 'ProgressNote',
      meta: { title: '病程记录' }
    })

    // 模拟真实场景：病人2添加"医嘱管理"标签
    this.simulateAddTab(patientId2, {
      name: 'OrderManagement',
      meta: { title: '医嘱管理' }
    })

    // 验证最终状态
    const finalState1 = simplePatientManager.getPatientTabsState(patientId1)
    const finalState2 = simplePatientManager.getPatientTabsState(patientId2)

    const finalIsolationCheck = 
      finalState1.activeRoute === 'ProgressNote' &&
      finalState2.activeRoute === 'OrderManagement' &&
      !finalState1.visitedViews.some(v => v.name === 'OrderManagement') &&
      !finalState2.visitedViews.some(v => v.name === 'ProgressNote')

    this.log('result', `最终隔离检查: ${finalIsolationCheck ? '通过' : '失败'}`, {
      patient1FinalState: {
        activeRoute: finalState1.activeRoute,
        tabNames: finalState1.visitedViews.map(v => v.name)
      },
      patient2FinalState: {
        activeRoute: finalState2.activeRoute,
        tabNames: finalState2.visitedViews.map(v => v.name)
      }
    })

    const overallSuccess = 
      results.eventBusTest.isDifferentInstance &&
      results.tabsStateTest.isIsolated &&
      finalIsolationCheck

    this.log('summary', `病人数据隔离测试 ${overallSuccess ? '全部通过' : '存在问题'}`, {
      eventBusIsolation: results.eventBusTest.isDifferentInstance,
      tabsStateIsolation: results.tabsStateTest.isIsolated,
      finalIsolationCheck
    })

    return {
      success: overallSuccess,
      results,
      finalIsolationCheck
    }
  }

  /**
   * 获取测试结果
   * @returns {Array} 测试结果列表
   */
  getTestResults() {
    return this.testResults
  }

  /**
   * 清理测试结果
   */
  clearTestResults() {
    this.testResults = []
  }

  /**
   * 获取当前系统状态
   * @returns {Object} 系统状态
   */
  getCurrentSystemState() {
    return {
      cachedPatients: simplePatientManager.getCachedPatientIds(),
      debugInfo: simplePatientManager.getDebugInfo(),
      currentPatient: simplePatientManager.getCurrentPatientId()
    }
  }
}

// 创建单例实例
const patientIsolationDebug = new PatientIsolationDebug()

// 在开发环境下暴露到全局
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.patientIsolationDebug = patientIsolationDebug
}

export default patientIsolationDebug
